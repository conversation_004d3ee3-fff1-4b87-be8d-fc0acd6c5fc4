Browserslist: caniuse-lite is outdated. Please run:
  npx update-browserslist-d[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by path *.js 90.5 MiB
  assets by status 61.9 MiB [emitted]
    assets by info 1.85 MiB [immutable] 128 assets
    assets by status 60 MiB [compared for emit]
      asset poll.process.js 6.02 MiB [emitted] [compared for emit] (name: poll.process) 1 related asset
      + 24 assets
  + 58 assets
assets by path *.css 9.86 MiB
  asset poll.process.css 2.05 MiB [compared for emit] (name: poll.process) 1 related asset
  asset layout-requests.css 1.74 MiB [compared for emit] (name: layout-requests) 1 related asset
  asset layout-base.css 1.73 MiB [compared for emit] (name: layout-base) 1 related asset
  asset layout-executor.css 1.03 MiB [compared for emit] (name: layout-executor) 1 related asset
  asset layout-external.css 1.03 MiB [compared for emit] (name: layout-external) 1 related asset
  asset main.2.css 278 KiB [compared for emit] (name: main.2) 1 related asset
  + 72 assets
runtime modules 261 KiB 648 modules
orphan modules 8.34 KiB [orphan] 7 modules
modules by path ./ko/ 10.1 MiB (javascript) 8.85 MiB (css/mini-extract)
  cacheable modules 10.1 MiB 4255 modules
  css modules 8.85 MiB 725 modules
  ./ko/utils/engine/ lazy ^.*$ namespace object 160 bytes [built] [code generated]
  ./ko/icons/ sync \.svg$ 7.72 KiB [built] [code generated]
modules by path ./node_modules/ 3.72 MiB (javascript) 47.8 KiB (css/mini-extract) 1260 modules
modules by path ./messages/ 493 KiB 123 modules
1 WARNING in child compilations (Use 'stats.children: true' resp. '--stats-children' for more details)
webpack 5.81.0 compiled with 1 warning in 73136 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.77c431ed74015f456c04.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.77c431ed74015f456c04.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6633 modules
runtime modules 200 KiB 384 modules
javascript modules 8.03 KiB
  ./ko/utils/engine/ lazy ^.*$ namespace object 160 bytes [built]
  ./ko/icons/ sync \.svg$ 7.72 KiB [built]
  ./messages/ lazy ^\.\/.*\.json$ namespace object 160 bytes [built]
webpack 5.81.0 compiled successfully in 8709 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.3fe46c3a41df08bca219.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.3fe46c3a41df08bca219.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.92 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 7314 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.96b87fb3606db82f6ed2.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.96b87fb3606db82f6ed2.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.97 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 9325 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.9d9230a322eedff46f35.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.9d9230a322eedff46f35.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.9 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 10019 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.bacc110b4ee1b81a1657.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.bacc110b4ee1b81a1657.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.87 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 6478 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 100 MiB [cached] 289 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 261 KiB (runtime) [cached] 7019 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.87 KiB [built]
webpack 5.81.0 compiled successfully in 4613 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.67598ca6dab143185337.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.67598ca6dab143185337.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.83 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 20039 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.c2444894cf1d1dbd8dea.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.c2444894cf1d1dbd8dea.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.88 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 23309 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 100 MiB [cached] 289 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 261 KiB (runtime) [cached] 7019 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.88 KiB [built]
webpack 5.81.0 compiled successfully in 8458 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.dee3ca79bbaa48a9ed57.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.dee3ca79bbaa48a9ed57.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.86 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 18317 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.ac05ac2698bf99a4892f.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.ac05ac2698bf99a4892f.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/pages/poll/stats/question-types/first-click-test/index.js 9.77 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 24176 ms
[webpack-cli] watching files for updates...
[webpack-cli] Compilation starting...
[webpack-cli] Compilation finished
assets by status 38.4 MiB [cached] 136 assets
assets by status 61.9 MiB [emitted]
  assets by info 1.85 MiB [immutable]
    asset ko_pages_poll_process_utils_question-formatter_js.16f9635191356dfc307f.js 1.12 MiB [emitted] [immutable] 1 related asset
    asset messages_de-DE_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_tr-TR_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_es-ES_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_fr-FR_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_it-IT_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_ja-JP_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_ko-KR_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_pt-PT_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    asset messages_zh-TW_main_json.16f9635191356dfc307f.js 28.5 KiB [emitted] [immutable]
    + 118 assets
  + 25 assets
cached modules 14.3 MiB (javascript) 8.9 MiB (css/mini-extract) 60.4 KiB (runtime) [cached] 6635 modules
runtime modules 200 KiB 384 modules
./ko/dialogs/stats/stats-heatmap-details-sidesheet/template.html 1.03 KiB [built] [code generated]
webpack 5.81.0 compiled successfully in 21452 ms
[webpack-cli] watching files for updates...
