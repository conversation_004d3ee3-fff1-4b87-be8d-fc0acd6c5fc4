import { FoquzComponent } from 'Models/foquz-component';
import { debounce } from 'lodash';

const MOBILE_BREAKPOINT = 768;

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params, element);

    this.imageSrc = ko.isObservable(params.imageSrc) ? params.imageSrc : ko.observable(params.imageSrc);
    this.imageWidth = ko.isObservable(params.imageWidth) ? params.imageWidth : ko.observable(params.imageWidth);
    this.imageHeight = ko.isObservable(params.imageHeight) ? params.imageHeight : ko.observable(params.imageHeight);
    this.size = params.size; // Optional: { width, height } for the container
    this.clicks = ko.isObservable(params.clicks) ? params.clicks : ko.observableArray(params.clicks || []);

    this.displayedImageDimensions = ko.observable({ width: 0, height: 0 });
    this.imageOffset = ko.observable({ x: 0, y: 0 }); // Offset of the image within its container
    this.containerElement = element.querySelector('.user-click-areas-showcase__container');
    this.imageElement = element.querySelector('.user-click-areas-showcase__image');
    
    this.activeClickZIndex = ko.observable(10); // To bring clicked item to front

    this.processedClicks = ko.computed(() => {
      const clicksArray = ko.unwrap(this.clicks);
      return clicksArray.map((click, index) => {
        const clickItem = {
          ...click,
          id: `click-indicator-${index}`,
          pixelX: ko.computed(() => (click.x / 100) * this.displayedImageDimensions().width + this.imageOffset().x),
          pixelY: ko.computed(() => (click.y / 100) * this.displayedImageDimensions().height + this.imageOffset().y),
          label: `${click.areaName} / ${click.timeInSeconds}s`,
          zIndex: ko.observable(1), // Default z-index
          labelStyle: ko.observable({})
        };
        
        // Recalculate label position when pixelX or pixelY changes
        this.addSubscription(clickItem.pixelX.subscribe(() => this.updateLabelPosition(clickItem)));
        this.addSubscription(clickItem.pixelY.subscribe(() => this.updateLabelPosition(clickItem)));
        
        return clickItem;
      });
    });
    
    this.debouncedUpdateDimensions = debounce(this.updateDimensions.bind(this), 100);
    this.resizeObserver = null;

    this.initialize();
  }

  initialize() {
    this.addSubscription(this.imageSrc.subscribe(() => this.loadImage()));
    this.addSubscription(this.imageWidth.subscribe(() => this.debouncedUpdateDimensions()));
    this.addSubscription(this.imageHeight.subscribe(() => this.debouncedUpdateDimensions()));
    this.addSubscription(this.clicks.subscribe(() => this.debouncedUpdateDimensions())); // Re-calc if clicks change

    this.loadImage();
    this.setupResizeObserver();
  }

  loadImage() {
    const src = ko.unwrap(this.imageSrc);
    if (!src) {
      this.displayedImageDimensions({ width: 0, height: 0 });
      this.imageOffset({ x: 0, y: 0 });
      return;
    }

    const img = new Image();
    img.onload = () => {
      if (!ko.unwrap(this.imageWidth) || !ko.unwrap(this.imageHeight)) {
        this.imageWidth(img.naturalWidth);
        this.imageHeight(img.naturalHeight);
      }
      this.debouncedUpdateDimensions();
    };
    img.onerror = () => {
      console.error('Failed to load image:', src);
      this.displayedImageDimensions({ width: 0, height: 0 });
      this.imageOffset({ x: 0, y: 0 });
    };
    img.src = src;
  }

  updateDimensions() {
    if (!this.containerElement || !ko.unwrap(this.imageSrc)) {
      this.displayedImageDimensions({ width: 0, height: 0 });
      this.imageOffset({ x: 0, y: 0 });
      return;
    }

    const containerRect = this.containerElement.getBoundingClientRect();
    const originalImageWidth = ko.unwrap(this.imageWidth);
    const originalImageHeight = ko.unwrap(this.imageHeight);

    if (!originalImageWidth || !originalImageHeight) {
        this.displayedImageDimensions({ width: 0, height: 0 });
        this.imageOffset({ x: 0, y: 0 });
        return;
    }

    let targetWidth = this.size ? this.size.width : containerRect.width;
    let targetHeight = this.size ? this.size.height : containerRect.height;
    
    // If no size prop, use container width and maintain aspect ratio for height
    if (!this.size) {
        targetHeight = (originalImageHeight / originalImageWidth) * targetWidth;
    }


    const imageAspect = originalImageWidth / originalImageHeight;
    const containerAspect = targetWidth / targetHeight;

    let displayedWidth, displayedHeight;

    if (imageAspect > containerAspect) { // Image is wider than container
      displayedWidth = targetWidth;
      displayedHeight = targetWidth / imageAspect;
    } else { // Image is taller or same aspect as container
      displayedHeight = targetHeight;
      displayedWidth = targetHeight * imageAspect;
    }
    
    // Ensure displayed dimensions do not exceed container if size is not specified
    if (!this.size) {
        if (displayedWidth > containerRect.width) {
            displayedWidth = containerRect.width;
            displayedHeight = displayedWidth / imageAspect;
        }
        if (displayedHeight > containerRect.height) {
            displayedHeight = containerRect.height;
            displayedWidth = displayedHeight * imageAspect;
        }
    }


    this.displayedImageDimensions({ width: displayedWidth, height: displayedHeight });

    const offsetX = (containerRect.width - displayedWidth) / 2;
    const offsetY = (containerRect.height - displayedHeight) / 2;
    this.imageOffset({ x: offsetX, y: offsetY });

    // After dimensions are updated, recalculate all label positions
    ko.unwrap(this.processedClicks).forEach(clickItem => this.updateLabelPosition(clickItem));
  }

  setupResizeObserver() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.containerElement) {
      this.resizeObserver = new ResizeObserver(() => {
        this.debouncedUpdateDimensions();
      });
      this.resizeObserver.observe(this.containerElement);
    }
  }
  
  updateLabelPosition(clickItem) {
    if (!this.imageElement) return;

    const indicatorElement = this.element.querySelector(`#${clickItem.id}`);
    const labelElement = this.element.querySelector(`#${clickItem.id}-label`);

    if (!indicatorElement || !labelElement) return;

    const imageRect = this.imageElement.getBoundingClientRect(); // Use image rect for bounds
    const containerRect = this.containerElement.getBoundingClientRect(); // Use container for relative positioning
    
    const indicatorRect = indicatorElement.getBoundingClientRect();
    const labelRect = labelElement.getBoundingClientRect();

    const indicatorCenterX = indicatorRect.left + indicatorRect.width / 2 - containerRect.left;
    const indicatorBottomY = indicatorRect.bottom - containerRect.top;
    
    let labelTop = indicatorBottomY + 5; // 5px gap
    let labelLeft;

    const isMobile = window.innerWidth <= MOBILE_BREAKPOINT;

    if (isMobile) {
      labelLeft = indicatorCenterX - labelRect.width / 2;
    } else {
      labelLeft = indicatorCenterX - labelRect.width / 2; // Default centered
    }
    
    // Ensure label is within image bounds (relative to imageElement)
    const imageDisplayedWidth = this.displayedImageDimensions().width;
    const imageDisplayedHeight = this.displayedImageDimensions().height;
    const imageOffsetX = this.imageOffset().x;
    const imageOffsetY = this.imageOffset().y;

    // Convert labelLeft and labelTop to be relative to the image itself for boundary checks
    let labelLeftRelativeToImage = labelLeft - imageOffsetX;
    let labelTopRelativeToImage = labelTop - imageOffsetY;

    // Check right boundary
    if (labelLeftRelativeToImage + labelRect.width > imageDisplayedWidth) {
      labelLeftRelativeToImage = imageDisplayedWidth - labelRect.width - (isMobile ? 5 : 0); // Add padding on mobile
    }
    // Check left boundary
    if (labelLeftRelativeToImage < 0) {
      labelLeftRelativeToImage = (isMobile ? 5 : 0); // Add padding on mobile
    }
    // Check bottom boundary
    if (labelTopRelativeToImage + labelRect.height > imageDisplayedHeight) {
        // Position label above indicator if it overflows bottom
        const indicatorTopY = indicatorRect.top - containerRect.top;
        labelTopRelativeToImage = (indicatorTopY - imageOffsetY) - labelRect.height - 5; // 5px gap
    }
    // Check top boundary (less common if positioned below, but good for robustness)
    if (labelTopRelativeToImage < 0) {
        labelTopRelativeToImage = 0;
    }

    // Convert back to container-relative positions for styling
    labelLeft = labelLeftRelativeToImage + imageOffsetX;
    labelTop = labelTopRelativeToImage + imageOffsetY;

    clickItem.labelStyle({
      top: `${labelTop}px`,
      left: `${labelLeft}px`,
    });
  }

  onIndicatorClick(clickItem) {
    this.activeClickZIndex(this.activeClickZIndex() + 1);
    clickItem.zIndex(this.activeClickZIndex());
  }

  dispose() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    super.dispose();
  }
}
