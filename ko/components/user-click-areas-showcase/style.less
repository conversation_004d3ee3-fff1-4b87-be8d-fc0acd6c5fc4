@import "../../../less/vars/colors.less";
@import "../../../less/parts/_mixins.less";

.user-click-areas-showcase {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden; // Prevents labels from showing outside if calculations are slightly off during resize

  &__container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__image {
    display: block;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; // Ensures the image is scaled correctly within its bounds
  }

  &__overlay {
    position: absolute;
    pointer-events: none; // Overlay itself should not capture events
  }

  &__click-item {
    position: absolute; // Positioned relative to the overlay
    pointer-events: auto; // Click items should capture events
    cursor: pointer;
  }

  &__indicator {
    position: absolute;
    width: 16px;
    height: 16px;
    background-color: #FF0000;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0px 5px 15px 0px rgba(46, 47, 49, 0.3);
    transform: translate(-50%, -50%); // Center the indicator on the x, y coordinates
    box-sizing: border-box;
  }

  &__label {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.2;
    white-space: nowrap;
    max-width: 200px;
    .text-ellipsis();
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
    // transform: translateX(-50%); // Centered by default, adjusted by JS
    z-index: 1; // Ensure labels are above indicators by default if they overlap slightly
  }
  
  &__no-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #888;
    font-style: italic;
  }
}

// Mobile specific styles
@media (max-width: 768px) {
  .user-click-areas-showcase {
    &__indicator {
      width: 14px;
      height: 14px;
      border-width: 2px;
    }

    &__label {
      font-size: 11px;
      padding: 3px 6px;
      // JS will handle horizontal centering for mobile
    }
  }
}
