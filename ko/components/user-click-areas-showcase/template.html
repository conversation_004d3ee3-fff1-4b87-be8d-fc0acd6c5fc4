<div class="user-click-areas-showcase__container">
  <!-- ko if: imageSrc -->
  <img class="user-click-areas-showcase__image"
       data-bind="attr: { src: imageSrc },
                  style: {
                    width: displayedImageDimensions().width + 'px',
                    height: displayedImageDimensions().height + 'px',
                    position: 'absolute',
                    top: imageOffset().y + 'px',
                    left: imageOffset().x + 'px'
                  }" />

  <div class="user-click-areas-showcase__overlay"
       data-bind="style: {
                    width: displayedImageDimensions().width + 'px',
                    height: displayedImageDimensions().height + 'px',
                    position: 'absolute',
                    top: imageOffset().y + 'px',
                    left: imageOffset().x + 'px'
                  }">
    <!-- ko foreach: processedClicks -->
    <div class="user-click-areas-showcase__click-item"
         data-bind="style: { zIndex: zIndex }, event: { mousedown: $parent.onIndicatorClick.bind($parent, $data) }">
      <div class="user-click-areas-showcase__indicator"
           data-bind="attr: { id: id }, style: {
                        top: pixelY() - $parent.imageOffset().y + 'px',
                        left: pixelX() - $parent.imageOffset().x + 'px'
                      }">
      </div>
      <div class="user-click-areas-showcase__label"
           data-bind="attr: { id: id + '-label' }, text: label, style: labelStyle">
      </div>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- ko ifnot: imageSrc -->
  <div class="user-click-areas-showcase__no-image">Изображение не указано</div>
  <!-- /ko -->
</div>
