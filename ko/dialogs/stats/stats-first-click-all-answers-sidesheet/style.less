@import 'Style/colors.less';

// ko/components/sidesheets/stats-first-click-all-answers-sidesheet/style.less

.first-click-answers-stats-sidesheet {

  &__table-row td,
  &__clicks-row td,
  &__skipped-row td,
  &__timeout-row td,
  &__no-clicks-row td,
  &__map-row td {
    padding-top: 10px;
    padding-bottom: 10px;
    border-style: solid;
  }

  &__clicks-row td {
    border-top-width: 0px;
    border-bottom-width: 1px;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  &__skipped-row td {
    border-bottom-width: 2px !important;
    color: @f-color-service;
  }
  &__timeout-row td {
    border-bottom-width: 2px !important;
  }
  &__no-clicks-row td {
    border-bottom-width: 2px !important;
    color: @f-color-service;
  }
  &__map-row td {
    border-top-width: 0px;
    border-bottom-width: 2px !important;
    padding-top: 10px;
    padding-bottom: 12px;
  }
}

.first-click-answers-stats-sidesheet-mobile-table {
  display: flex;
  flex-direction: column;
  margin-left: -15px;
  margin-right: -15px;

  &__client-card {
    background: #fff;
    border-top: 2px solid #E7EBED;
    padding: 12px 15px 0 15px;
    display: flex;
    flex-direction: column;
    &:last-child {
      border-bottom: 2px solid #E7EBED;
    }
  }

  &__client-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  &__field {
    display: flex;
    flex-direction: row;
    gap: 25px;
  }

  &__field-label {
    font-size: 12px;
    color: @f-color-text;
    font-weight: 700;
    flex: 0 0 auto;
    width: 80px;
  }

  &__field-value {
    font-size: 12px;
    color: @f-color-text;
    word-break: break-word;
    flex-grow: 1;
  }

  &__comment-section {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding-top: 15px;
    padding-bottom: 10px;
  }

  &__comment-label {
    font-size: 12px;
    color: @f-color-text;
    font-weight: 700;
  }

  &__clicks-section {
    padding-top: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__status-message {
    font-size: 12px;
    padding-top: 10px;
    padding-bottom: 12px;
    line-height: 1.2;
    border-top: 1px solid #E7EBED;
    
    &--skipped {
      color: @f-color-service;
    }
    
    &--timeout {
      color: @f-color-danger;
    }
    
    &--no-clicks {
      color: @f-color-service;
    }
  }

  &__clicks {
    display: flex;
    flex-direction: column;
  }

  &__click-item {
    display: flex;
    align-items: center;
    padding: 9px 0;
    border-bottom: 1px solid #E7EBED;
    gap: 15px;
    &:first-child {
      border-top: 1px solid #E7EBED;
    }
  }

  &__click-label {
    font-size: 12px;
    color: @f-color-text;
    font-weight: 500;
    line-height: 1.3;
    flex-grow: 1;
  }

  &__click-time {
    font-size: 12px;
    color: @f-color-service;
    line-height: 1.15;
    flex: 0 0 50px;
  }

  &__link {
    color: @f-color-primary;
    text-decoration: none;
    font-size: 12px;
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 1.3;
    display: block;
    
    &--primary {
      font-weight: 500;
    }
    
    &:hover {
      text-decoration: underline;
    }
  }
}
