import { Client } from '../client';

export class FirstClickAnswersClient extends Client {
  constructor(data) {
    super({
      name: data.name,
      email: data.email,
      phone: data.phone,
      passedAt: data.passedAt,
      orderId: data.orderId,
      orderCreatedAt: data.orderCreatedAt,
      comment: data.comment,
      points: data.points, // if applicable, though likely not for first-click
      filialName: data.filialName,
      skipped: data.skipped, // Pass skipped status
      timeout: data.timeout, // Pass timeout status
      without_points: data.without_points // if points are not applicable
    });

    // First-click specific fields
    // The 'answer' array from API now seems to contain strings like "x,y,time"
    this.clicks = (data.answer || []).map((clickString, index) => {
      if (index === 0) { // Log only the first click string to verify
        console.log('Raw click string from API (first one):', clickString);
      }
      const parts = typeof clickString === 'string' ? clickString.split(',') : [];
      return {
        x: parts.length > 0 ? parseFloat(parts[0]) : undefined,
        y: parts.length > 1 ? parseFloat(parts[1]) : undefined,
        timeInSeconds: parts.length > 2 ? parseFloat(parts[2]) : undefined,
        label: `Пользовательская точка ${index + 1}`
      };
    });
    
    this.timeToFirstClick = data.time_to_first_click;
    this.skipped = data.skipped;

    console.log('FirstClickAnswersClient clicks', this.clicks);
    console.log('FirstClickAnswersClient timeToFirstClick', this.timeToFirstClick);
    console.log('FirstClickAnswersClient skipped', this.skipped);
  }
}
