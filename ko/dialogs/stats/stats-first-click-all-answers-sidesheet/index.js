import { StatsDialog } from '../dialog';
import html from './template.html';
import './style.less';
import { Table } from './table'; // We'll create this next

class StatsFirstClickAllAnswersSidesheetModel extends StatsDialog {
  constructor(params, element) {
    super(params, element);
    this.filterParams = params.filterParams || {};
  }

  createTable() {
    return new Table();
  }

  processAnswerData(answer) {
    return {
      ...answer,
      respondentName: answer.respondent_name || `Респондент ${answer.respondent_id || answer.id}`,
      answerTime: answer.time_to_first_click !== null ? `${answer.time_to_first_click} сек` : 'N/A',
      status: this.getAnswerStatus(answer),
      clickPoints: (answer.click_points || []).map(point => ({
        ...point,
        areaName: this.getAreaNameById(point.area_id) || 'Вне области',
        timeFormatted: point.time !== null ? `${point.time} сек` : 'N/A'
      })),
      commentText: answer.comment || null,
      mapId: ko.utils.uniqueId('answer-map-')
    };
  }

  getAreaNameById(areaId) {
    if (!areaId || !this.question.processedAreas) return null;
    const area = this.question.processedAreas.find(a => a.id === areaId);
    return area ? area.name : null;
  }

  getAnswerStatus(answer) {
    // This might also be adjusted based on API response
    if (answer.skipped) return 'Пропущен';
    if (answer.timeout) return 'Время вышло'; // Assuming a timeout flag
    if (answer.click_points && answer.click_points.length > 0) return 'Ответил';
    return 'Нет данных';
  }
  
  showClickOnMap(answer, clickPoint) {
    console.log('showClickOnMap in StatsFirstClickAllAnswersSidesheetModel', answer, clickPoint);
    // Implementation for showing click on map will be needed
  }

  openClicksMapSidesheet() {
    console.log('openClicksMapSidesheet in StatsFirstClickAllAnswersSidesheetModel');
    console.log('openClicksMapSidesheet table', this.table);
    this.ctx.openStatsModal("stats-user-click-areas-sidesheet", {
      question: this.question,
      title: this.question.name,
      imageSrc: this.question.imageSrc,
      imageWidth: this.question.imageWidth,
      imageHeight: this.question.imageHeight,
      clicks: this.question.clicks,
      size: this.question.size,
      answer: {click_points: []},
    });
  }

  // close method is inherited from DialogWrapper via StatsDialog
}

ko.components.register('stats-first-click-all-answers-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      // Add a class for specific styling if needed, similar to variants-sidesheet
      element.classList.add('stats-first-click-all-answers-sidesheet-wrapper'); 
      return new StatsFirstClickAllAnswersSidesheetModel(params, element);
    },
  },
  template: html
});

export default StatsFirstClickAllAnswersSidesheetModel;
