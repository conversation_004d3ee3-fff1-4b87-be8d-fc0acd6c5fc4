import { DialogWrapper } from 'Dialogs/wrapper';
import html from './template.html';
import './style.less';

class StatsUserClickAreasSidesheet extends DialogWrapper {
  constructor(params = {}, element) {
    super(params, element);

    this.question = params.question; // Expecting a question object
    this.answer = params.answer;   // Expecting specific answer data
    
    this.title = ko.observable(params.title || 'Карта кликов пользователя');

    // Parameters for the user-click-areas-showcase component
    this.imageSrc = ko.observable(ko.unwrap(this.question?.imageSrc) || ko.unwrap(this.question?.imageUrl));
    this.imageWidth = ko.observable(ko.unwrap(this.question?.imageWidth) || null);
    this.imageHeight = ko.observable(ko.unwrap(this.question?.imageHeight) || null);
    
    // Process clicks from the answer data
    this.clicksData = ko.computed(() => {
      if (!this.answer || !ko.unwrap(this.answer.click_points)) {
        return [];
      }
      // Transform answer.click_points to the format expected by user-click-areas-showcase
      // {x, y, areaName, timeInSeconds}
      return ko.unwrap(this.answer.click_points).map(click => ({
        x: click.x_percent, // Assuming API provides x_percent, y_percent
        y: click.y_percent,
        areaName: click.area_name || `Клик #${click.id}`, // Fallback name
        timeInSeconds: click.time_to_click !== undefined ? click.time_to_click.toFixed(1) : 'N/A'
      }));
    });

    // Optional size for the showcase component, can be undefined
    this.showcaseSize = params.showcaseSize || undefined; 
  }

  onModalInit(...args) {
    super.onModalInit(...args);
    // Any specific initialization after modal is ready
  }
}

ko.components.register('stats-user-click-areas-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      const element = componentInfo.element;
      element.classList.add('stats-user-click-areas-sidesheet');
      return new StatsUserClickAreasSidesheet(params, element);
    },
  },
  template: html,
});

export default StatsUserClickAreasSidesheet;
