<!-- ko let: { $sidesheet: $component } -->
<sidesheet class="stats-user-click-areas-sidesheet-wrapper" params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__body pt-4 pb-4">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">
        <div class="stats-sidesheet__header mb-3">
          <div class="px-2">
            <h2 class="foquz-dialog__title" data-bind="text: title"></h2>
            <!-- ko if: answer && answer.respondentName -->
            <p class="text-muted mb-0">Респондент: <span data-bind="text: answer.respondentName"></span></p>
            <!-- /ko -->
          </div>
        </div>

        <div class="stats-user-click-areas-sidesheet__showcase-container">
          <!-- ko if: imageSrc -->
          <user-click-areas-showcase params="
            imageSrc: imageSrc,
            imageWidth: imageWidth,
            imageHeight: imageHeight,
            clicks: clicksData,
            size: showcaseSize">
          </user-click-areas-showcase>
          <!-- /ko -->
          <!-- ko ifnot: imageSrc -->
          <div class="alert alert-warning">Изображение для отображения карты кликов не найдено.</div>
          <!-- /ko -->
        </div>

      </div>
    </div>
  </div>
</sidesheet>
<!-- /ko -->
