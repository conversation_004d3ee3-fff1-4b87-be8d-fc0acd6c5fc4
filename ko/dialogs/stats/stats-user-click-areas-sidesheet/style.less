@import "../../../less/vars/colors.less";
@import "../../../less/parts/_mixins.less";

.stats-user-click-areas-sidesheet-wrapper {
  .stats-sidesheet__header {
    // Styles for the header if needed
  }

  .stats-user-click-areas-sidesheet__showcase-container {
    width: 100%;
    height: 500px; // Default height, can be overridden by showcaseSize or image aspect ratio
    min-height: 300px; // Minimum height to ensure visibility
    position: relative; // For the showcase component to position itself
    border: 1px solid @f-color-border-light;
    margin-bottom: 20px;

    user-click-areas-showcase {
      width: 100%;
      height: 100%;
    }
  }

  .alert-warning {
    margin: 20px;
  }
}
